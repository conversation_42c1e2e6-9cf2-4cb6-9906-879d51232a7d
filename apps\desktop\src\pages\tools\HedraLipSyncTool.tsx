import React, { useState, useCallback } from 'react';
import {
  MessageCircle,
  Upload,
  Image,
  Music,
  Play,
  Download,
  RefreshCw,
  CheckCircle,
  XCircle,
  Loader2,
  AlertCircle,
  FileImage,
  FileAudio
} from 'lucide-react';
import { open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import { useNotifications } from '../../components/NotificationSystem';
import {
  HedraLipSyncState,
  HedraFileUploadRequest,
  HedraTaskSubmitRequest,
  HedraTaskStatusParams,
  FileUploadResponse,
  TaskResponse,
  TaskStatusResponse
} from '../../types/hedraLipSync';

/**
 * Hedra 口型合成工具
 * 基于 Hedra API 实现图片与音频的口型同步
 */
const HedraLipSyncTool: React.FC = () => {
  const { addNotification } = useNotifications();

  // 主要状态
  const [state, setState] = useState<HedraLipSyncState>({
    imageFile: null,
    audioFile: null,
    task: { status: 'idle' },
    isProcessing: false
  });

  // 文件选择处理 - 直接选择文件路径
  const handleImageSelect = useCallback(async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: '图片文件',
          extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        }]
      });

      if (!selected || typeof selected !== 'string') {
        return;
      }

      setState(prev => ({
        ...prev,
        imageFile: {
          file: { name: selected.split('\\').pop() || selected } as File,
          filePath: selected,
          uploadStatus: 'idle'
        }
      }));

      addNotification({
        type: 'success',
        title: '图片已选择',
        message: `已选择图片：${selected.split('\\').pop() || selected}`
      });
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  }, [addNotification]);

  const handleAudioSelect = useCallback(async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: '音频文件',
          extensions: ['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg']
        }]
      });

      if (!selected || typeof selected !== 'string') {
        return;
      }

      setState(prev => ({
        ...prev,
        audioFile: {
          file: { name: selected.split('\\').pop() || selected } as File,
          filePath: selected,
          uploadStatus: 'idle'
        }
      }));

      addNotification({
        type: 'success',
        title: '音频已选择',
        message: `已选择音频：${selected.split('\\').pop() || selected}`
      });
    } catch (error) {
      console.error('选择音频失败:', error);
    }
  }, [addNotification]);

  // 上传已选择的文件到服务器
  const uploadFileToServer = useCallback(async (filePath: string, purpose: 'image' | 'audio'): Promise<string> => {
    try {
      console.log(`上传 ${purpose} 文件:`, filePath);

      const request: HedraFileUploadRequest = {
        file_path: filePath,
        purpose
      };

      const response: FileUploadResponse = await invoke('hedra_upload_file', { params: request });
      if (response.status && response.data) {
        return response.data;
      } else {
        throw new Error(response.msg || '文件上传失败');
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }, []);



  // 轮询任务状态
  const pollTaskStatus = useCallback(async (taskId: string) => {
    const maxAttempts = 60; // 最多轮询60次（5分钟）
    let attempts = 0;

    const poll = async () => {
      try {
        const statusParams: HedraTaskStatusParams = { task_id: taskId };
        const statusResponse: TaskStatusResponse = await invoke('hedra_query_task_status', { params: statusParams });

        setState(prev => ({
          ...prev,
          task: {
            ...prev.task,
            status: statusResponse.status === 'success' ? 'completed' : 'processing',
            progress: statusResponse.progress,
            resultUrl: statusResponse.result?.video_url || statusResponse.result?.url,
            errorMessage: statusResponse.error,
            updatedAt: new Date().toISOString()
          }
        }));

        if (statusResponse.status === 'success') {
          setState(prev => ({ ...prev, isProcessing: false }));
          addNotification({
            type: 'success',
            title: '任务完成',
            message: '口型合成视频已生成完成！'
          });
          return;
        }

        if (statusResponse.status === 'failed' || statusResponse.error) {
          setState(prev => ({
            ...prev,
            task: {
              ...prev.task,
              status: 'failed',
              errorMessage: statusResponse.error || '任务处理失败'
            },
            isProcessing: false
          }));
          addNotification({
            type: 'error',
            title: '任务失败',
            message: statusResponse.error || '口型合成任务处理失败'
          });
          return;
        }

        // 继续轮询
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // 5秒后再次查询
        } else {
          setState(prev => ({
            ...prev,
            task: {
              ...prev.task,
              status: 'failed',
              errorMessage: '任务超时'
            },
            isProcessing: false
          }));
          addNotification({
            type: 'error',
            title: '任务超时',
            message: '任务处理时间过长，请稍后手动查询结果'
          });
        }

      } catch (error) {
        console.error('查询任务状态失败:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setState(prev => ({ ...prev, isProcessing: false }));
        }
      }
    };

    poll();
  }, [addNotification]);

  // 提交口型合成任务
  const submitLipSyncTask = useCallback(async () => {
    console.log('submitLipSyncTask 被调用');
    console.log('当前状态:', state);

    if (!state.imageFile || !state.audioFile) {
      addNotification({
        type: 'error',
        title: '文件缺失',
        message: '请先选择图片和音频文件'
      });
      return;
    }

    setState(prev => ({ ...prev, isProcessing: true }));

    try {
      console.log('开始处理任务...');
      addNotification({
        type: 'info',
        title: '开始处理',
        message: '正在上传文件并提交任务...'
      });

      // 1. 上传图片文件
      console.log('开始上传图片文件...');
      setState(prev => ({
        ...prev,
        imageFile: prev.imageFile ? { ...prev.imageFile, uploadStatus: 'uploading' } : null
      }));

      const imageUrl = await uploadFileToServer(state.imageFile.filePath!, 'image');
      console.log('图片上传完成，URL:', imageUrl);

      setState(prev => ({
        ...prev,
        imageFile: prev.imageFile ? { ...prev.imageFile, uploadStatus: 'uploaded', url: imageUrl } : null
      }));

      // 2. 上传音频文件
      console.log('开始上传音频文件...');
      setState(prev => ({
        ...prev,
        audioFile: prev.audioFile ? { ...prev.audioFile, uploadStatus: 'uploading' } : null
      }));

      const audioUrl = await uploadFileToServer(state.audioFile.filePath!, 'audio');
      console.log('音频上传完成，URL:', audioUrl);

      setState(prev => ({
        ...prev,
        audioFile: prev.audioFile ? { ...prev.audioFile, uploadStatus: 'uploaded', url: audioUrl } : null
      }));

      // 3. 提交口型合成任务
      console.log('开始提交任务...');
      setState(prev => ({
        ...prev,
        task: { status: 'submitting' }
      }));

      // 根据后端API，使用URL而不是File对象
      const taskRequest: HedraTaskSubmitRequest = {
        audio_file: audioUrl,
        img_file: imageUrl
      };
      console.log('任务请求参数:', taskRequest);

      const taskResponse: TaskResponse = await invoke('hedra_submit_task', { params: taskRequest });
      console.log('任务提交响应:', taskResponse);

      if (taskResponse.task_id) {
        setState(prev => ({
          ...prev,
          task: {
            status: 'processing',
            taskId: taskResponse.task_id,
            createdAt: new Date().toISOString()
          }
        }));

        addNotification({
          type: 'success',
          title: '任务已提交',
          message: `口型合成任务已开始处理，任务ID：${taskResponse.task_id}`
        });

        // 开始轮询任务状态
        pollTaskStatus(taskResponse.task_id);
      } else {
        throw new Error('任务提交失败：未获取到任务ID');
      }

    } catch (error) {
      console.error('口型合成任务失败:', error);

      setState(prev => ({
        ...prev,
        task: {
          status: 'failed',
          errorMessage: error instanceof Error ? error.message : '未知错误'
        },
        isProcessing: false
      }));

      addNotification({
        type: 'error',
        title: '任务失败',
        message: error instanceof Error ? error.message : '口型合成任务失败'
      });
    }
  }, [uploadFileToServer, addNotification, pollTaskStatus, state]);

  // 重置状态
  const resetState = useCallback(() => {
    setState({
      imageFile: null,
      audioFile: null,
      task: { status: 'idle' },
      isProcessing: false
    });

    // 重置完成，不需要清理input元素
  }, []);

  // 下载结果
  const downloadResult = useCallback(() => {
    if (state.task.resultUrl) {
      const link = document.createElement('a');
      link.href = state.task.resultUrl;
      link.download = `hedra_lipsync_${state.task.taskId || Date.now()}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [state.task.resultUrl, state.task.taskId]);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="page-header flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300">
            <MessageCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-purple-600 bg-clip-text text-transparent">
              Hedra 口型合成工具
            </h1>
            <p className="text-gray-600 text-lg">让静态图片"开口说话"，AI 驱动的口型同步技术</p>
          </div>
        </div>

        {/* 重置按钮 */}
        <button
          onClick={resetState}
          disabled={state.isProcessing}
          className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className="w-4 h-4" />
          重置
        </button>
      </div>

      {/* 文件上传区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 图片上传 */}
        <div className="card p-6">
          <div className="flex items-center gap-3 mb-4">
            <Image className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">选择图片</h3>
          </div>
          
          <div className="space-y-4">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
              onClick={handleImageSelect}
            >
              {state.imageFile ? (
                <div className="space-y-3">
                  <FileImage className="w-12 h-12 text-blue-600 mx-auto" />
                  <div>
                    <p className="font-medium text-gray-900">{state.imageFile.file.name}</p>
                    <p className="text-sm text-gray-500">
                      {state.imageFile.filePath}
                    </p>
                  </div>
                  {state.imageFile.uploadStatus === 'uploading' && (
                    <div className="flex items-center justify-center gap-2 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">上传中...</span>
                    </div>
                  )}
                  {state.imageFile.uploadStatus === 'uploaded' && (
                    <div className="flex items-center justify-center gap-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">上传完成</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">点击选择图片</p>
                    <p className="text-sm text-gray-500">支持 JPG、PNG、GIF 格式</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 音频上传 */}
        <div className="card p-6">
          <div className="flex items-center gap-3 mb-4">
            <Music className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">选择音频</h3>
          </div>
          
          <div className="space-y-4">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-400 transition-colors cursor-pointer"
              onClick={handleAudioSelect}
            >
              {state.audioFile ? (
                <div className="space-y-3">
                  <FileAudio className="w-12 h-12 text-green-600 mx-auto" />
                  <div>
                    <p className="font-medium text-gray-900">{state.audioFile.file.name}</p>
                    <p className="text-sm text-gray-500">
                      {(state.audioFile.file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  {state.audioFile.uploadStatus === 'uploading' && (
                    <div className="flex items-center justify-center gap-2 text-green-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">上传中...</span>
                    </div>
                  )}
                  {state.audioFile.uploadStatus === 'uploaded' && (
                    <div className="flex items-center justify-center gap-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">上传完成</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">点击选择音频</p>
                    <p className="text-sm text-gray-500">支持 MP3、WAV、M4A 格式</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="card p-6">
        <div className="flex items-center justify-center">
          <button
            onClick={submitLipSyncTask}
            disabled={!state.imageFile || !state.audioFile || state.isProcessing}
            className="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-lg font-medium text-lg"
          >
            {state.isProcessing ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                处理中...
              </>
            ) : (
              <>
                <Play className="w-5 h-5" />
                开始口型合成
              </>
            )}
          </button>
        </div>
      </div>

      {/* 任务状态和结果 */}
      {state.task.status !== 'idle' && (
        <div className="card p-6">
          <div className="flex items-center gap-3 mb-4">
            <MessageCircle className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">任务状态</h3>
          </div>

          <div className="space-y-4">
            {/* 状态显示 */}
            <div className="flex items-center gap-3">
              {state.task.status === 'submitting' && (
                <>
                  <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                  <span className="text-blue-600 font-medium">提交任务中...</span>
                </>
              )}
              {state.task.status === 'processing' && (
                <>
                  <Loader2 className="w-5 h-5 text-purple-600 animate-spin" />
                  <span className="text-purple-600 font-medium">处理中...</span>
                  {state.task.progress && (
                    <span className="text-sm text-gray-500">({state.task.progress}%)</span>
                  )}
                </>
              )}
              {state.task.status === 'completed' && (
                <>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-green-600 font-medium">处理完成</span>
                </>
              )}
              {state.task.status === 'failed' && (
                <>
                  <XCircle className="w-5 h-5 text-red-600" />
                  <span className="text-red-600 font-medium">处理失败</span>
                </>
              )}
            </div>

            {/* 任务信息 */}
            {state.task.taskId && (
              <div className="text-sm text-gray-600">
                <p>任务ID: {state.task.taskId}</p>
                {state.task.createdAt && (
                  <p>创建时间: {new Date(state.task.createdAt).toLocaleString()}</p>
                )}
              </div>
            )}

            {/* 错误信息 */}
            {state.task.errorMessage && (
              <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">错误信息</p>
                  <p className="text-red-700">{state.task.errorMessage}</p>
                </div>
              </div>
            )}

            {/* 结果下载 */}
            {state.task.status === 'completed' && state.task.resultUrl && (
              <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">口型合成完成</p>
                    <p className="text-green-700">视频已生成，可以下载查看</p>
                  </div>
                </div>
                <button
                  onClick={downloadResult}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  下载视频
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HedraLipSyncTool;
