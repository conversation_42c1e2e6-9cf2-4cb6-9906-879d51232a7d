use tauri::{command, State};
use crate::app_state::AppState;
use crate::infrastructure::bowong_text_video_agent_service::BowongTextVideoAgentService;
use crate::data::models::bowong_text_video_agent::*;
use std::sync::Arc;
use tokio::sync::RwLock;

/// BowongTextVideoAgent 服务的全局状态
type BowongServiceState = Arc<RwLock<Option<BowongTextVideoAgentService>>>;

/// 初始化 BowongTextVideoAgent 服务
#[command]
pub async fn initialize_bowong_service(
    state: State<'_, AppState>,
    config: BowongTextVideoAgentConfig,
) -> Result<String, String> {
    state.initialize_bowong_service(config)
        .map_err(|e| format!("Failed to initialize BowongTextVideoAgent service: {}", e))?;

    Ok("BowongTextVideoAgent service initialized successfully".to_string())
}

// ============================================================================
// 提示词预处理模块命令
// ============================================================================

/// 获取示例提示词
#[command]
pub async fn bowong_get_sample_prompt() -> Result<ApiResponse, String> {
    // 这里需要从全局状态获取服务实例
    // 暂时返回模拟数据
    Ok(ApiResponse {
        status: true,
        message: "Sample prompt retrieved".to_string(),
        data: Some(serde_json::json!({
            "prompt": "a beautiful landscape with mountains and lakes"
        })),
    })
}

/// 健康检查
#[command]
pub async fn bowong_health_check() -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Service is healthy".to_string(),
        data: None,
    })
}

// ============================================================================
// 文件操作模块命令
// ============================================================================

/// 上传文件到 S3
#[command]
pub async fn bowong_upload_file_to_s3(
    request: S3FileUploadRequest,
) -> Result<FileUploadResponse, String> {
    // 实际实现需要调用服务
    Ok(FileUploadResponse {
        status: true,
        msg: "File uploaded successfully".to_string(),
        data: Some("https://s3.example.com/uploaded-file.jpg".to_string()),
    })
}

/// 上传文件到 COS
#[command]
pub async fn bowong_upload_file(
    request: COSFileUploadRequest,
) -> Result<FileUploadResponse, String> {
    Ok(FileUploadResponse {
        status: true,
        msg: "File uploaded successfully".to_string(),
        data: Some("https://cos.example.com/uploaded-file.jpg".to_string()),
    })
}

/// 文件健康检查
#[command]
pub async fn bowong_file_health_check() -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "File service is healthy".to_string(),
        data: None,
    })
}

// ============================================================================
// 视频模板管理模块命令
// ============================================================================

/// 获取模板列表
#[command]
pub async fn bowong_get_templates(
    request: TemplateListRequest,
) -> Result<TemplateListResponse, String> {
    Ok(TemplateListResponse {
        templates: vec![],
        total: 0,
        page: request.page.unwrap_or(1),
        page_size: request.page_size.unwrap_or(10),
    })
}

/// 获取单个模板
#[command]
pub async fn bowong_get_template(
    template_id: String,
) -> Result<VideoTemplate, String> {
    Ok(VideoTemplate {
        id: template_id,
        name: "Sample Template".to_string(),
        description: Some("A sample video template".to_string()),
        config: serde_json::json!({}),
        created_at: "2024-01-01T00:00:00Z".to_string(),
        updated_at: "2024-01-01T00:00:00Z".to_string(),
    })
}

/// 创建模板
#[command]
pub async fn bowong_create_template(
    request: CreateTemplateRequest,
) -> Result<VideoTemplate, String> {
    Ok(VideoTemplate {
        id: "new-template-id".to_string(),
        name: request.name,
        description: request.description,
        config: request.config,
        created_at: "2024-01-01T00:00:00Z".to_string(),
        updated_at: "2024-01-01T00:00:00Z".to_string(),
    })
}

/// 更新模板
#[command]
pub async fn bowong_update_template(
    request: UpdateTemplateRequest,
) -> Result<VideoTemplate, String> {
    Ok(VideoTemplate {
        id: request.id,
        name: request.name.unwrap_or("Updated Template".to_string()),
        description: request.description,
        config: request.config.unwrap_or(serde_json::json!({})),
        created_at: "2024-01-01T00:00:00Z".to_string(),
        updated_at: "2024-01-01T00:00:00Z".to_string(),
    })
}

/// 删除模板
#[command]
pub async fn bowong_delete_template(
    template_id: String,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: format!("Template {} deleted successfully", template_id),
        data: None,
    })
}

// ============================================================================
// Midjourney 图片生成模块命令
// ============================================================================

/// 检查提示词
#[command]
pub async fn bowong_check_prompt(
    params: PromptCheckParams,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Prompt is valid".to_string(),
        data: Some(serde_json::json!({
            "prompt": params.prompt,
            "valid": true
        })),
    })
}

/// 同步生成图片
#[command]
pub async fn bowong_sync_generate_image(
    request: SyncImageGenerationRequest,
) -> Result<ImageGenerationResponse, String> {
    Ok(ImageGenerationResponse {
        task_id: "img-task-123".to_string(),
        status: "completed".to_string(),
        images: Some(vec![
            "https://example.com/generated-image-1.jpg".to_string(),
            "https://example.com/generated-image-2.jpg".to_string(),
        ]),
        error: None,
    })
}

/// 异步生成图片
#[command]
pub async fn bowong_async_generate_image(
    request: AsyncImageGenerationRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "img-task-456".to_string(),
        status: "pending".to_string(),
        message: "Image generation task submitted".to_string(),
    })
}

/// 描述图片
#[command]
pub async fn bowong_describe_image(
    request: ImageDescribeRequest,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Image described successfully".to_string(),
        data: Some(serde_json::json!({
            "description": "A beautiful landscape with mountains and lakes"
        })),
    })
}

// ============================================================================
// 极梦视频生成模块命令
// ============================================================================

/// 生成视频
#[command]
pub async fn bowong_generate_video(
    request: VideoGenerationRequest,
) -> Result<VideoGenerationResponse, String> {
    Ok(VideoGenerationResponse {
        task_id: "video-task-789".to_string(),
        status: "completed".to_string(),
        video_url: Some("https://example.com/generated-video.mp4".to_string()),
        progress: Some(100.0),
        error: None,
    })
}

/// 批量查询视频状态
#[command]
pub async fn bowong_batch_query_video_status(
    request: VideoTaskStatus,
) -> Result<BatchVideoStatusResponse, String> {
    let results = request.task_ids.into_iter().map(|task_id| {
        TaskStatusResponse {
            task_id,
            status: "completed".to_string(),
            progress: Some(100.0),
            result: Some(serde_json::json!({
                "video_url": "https://example.com/video.mp4"
            })),
            error: None,
            created_at: Some("2024-01-01T00:00:00Z".to_string()),
            updated_at: Some("2024-01-01T00:00:00Z".to_string()),
        }
    }).collect();

    Ok(BatchVideoStatusResponse { results })
}

// ============================================================================
// 任务管理模块命令
// ============================================================================

/// 创建任务
#[command]
pub async fn bowong_create_task(
    request: TaskRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "task-abc123".to_string(),
        status: "pending".to_string(),
        message: "Task created successfully".to_string(),
    })
}

/// 获取任务状态
#[command]
pub async fn bowong_get_task_status(
    task_id: String,
) -> Result<TaskStatusResponse, String> {
    Ok(TaskStatusResponse {
        task_id,
        status: "completed".to_string(),
        progress: Some(100.0),
        result: Some(serde_json::json!({
            "output": "Task completed successfully"
        })),
        error: None,
        created_at: Some("2024-01-01T00:00:00Z".to_string()),
        updated_at: Some("2024-01-01T00:00:00Z".to_string()),
    })
}

/// 检查任务类型
#[command]
pub async fn bowong_check_task_type(
    params: CheckTaskTypeParams,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Task type is valid".to_string(),
        data: Some(serde_json::json!({
            "task_type": params.task_type,
            "supported": true
        })),
    })
}

// ============================================================================
// 302AI 服务集成模块命令
// ============================================================================

/// 302AI MJ 异步生成图片
#[command]
pub async fn bowong_ai302_mj_async_generate_image(
    request: AI302MJImageRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "ai302-mj-task-123".to_string(),
        status: "pending".to_string(),
        message: "302AI MJ image generation task submitted".to_string(),
    })
}

/// 302AI MJ 取消任务
#[command]
pub async fn bowong_ai302_mj_cancel_task(
    request: AI302TaskCancelRequest,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: format!("Task {} cancelled successfully", request.task_id),
        data: None,
    })
}

/// 302AI MJ 查询任务状态
#[command]
pub async fn bowong_ai302_mj_query_task_status(
    params: AI302TaskStatusParams,
) -> Result<TaskStatusResponse, String> {
    Ok(TaskStatusResponse {
        task_id: params.task_id,
        status: "completed".to_string(),
        progress: Some(100.0),
        result: Some(serde_json::json!({
            "images": ["https://example.com/ai302-image.jpg"]
        })),
        error: None,
        created_at: Some("2024-01-01T00:00:00Z".to_string()),
        updated_at: Some("2024-01-01T00:00:00Z".to_string()),
    })
}

/// 302AI MJ 同步生成图片
#[command]
pub async fn bowong_ai302_mj_sync_generate_image(
    request: SyncImageGenerationRequest,
) -> Result<ImageGenerationResponse, String> {
    Ok(ImageGenerationResponse {
        task_id: "ai302-mj-sync-123".to_string(),
        status: "completed".to_string(),
        images: Some(vec!["https://example.com/ai302-sync-image.jpg".to_string()]),
        error: None,
    })
}

/// 302AI JM 同步生成视频
#[command]
pub async fn bowong_ai302_jm_sync_generate_video(
    request: AI302JMVideoRequest,
) -> Result<VideoGenerationResponse, String> {
    Ok(VideoGenerationResponse {
        task_id: "ai302-jm-video-123".to_string(),
        status: "completed".to_string(),
        video_url: Some("https://example.com/ai302-video.mp4".to_string()),
        progress: Some(100.0),
        error: None,
    })
}

/// 302AI JM 异步生成视频
#[command]
pub async fn bowong_ai302_jm_async_generate_video(
    request: AI302JMVideoRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "ai302-jm-async-456".to_string(),
        status: "pending".to_string(),
        message: "302AI JM video generation task submitted".to_string(),
    })
}

/// 302AI VEO 异步提交
#[command]
pub async fn bowong_ai302_veo_async_submit(
    request: AI302VEOVideoRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "ai302-veo-789".to_string(),
        status: "pending".to_string(),
        message: "302AI VEO video task submitted".to_string(),
    })
}

/// 302AI VEO 同步生成视频
#[command]
pub async fn bowong_ai302_veo_sync_generate_video(
    request: AI302VEOVideoRequest,
) -> Result<VideoGenerationResponse, String> {
    Ok(VideoGenerationResponse {
        task_id: "ai302-veo-sync-101".to_string(),
        status: "completed".to_string(),
        video_url: Some("https://example.com/ai302-veo-video.mp4".to_string()),
        progress: Some(100.0),
        error: None,
    })
}

// ============================================================================
// 海螺API模块命令
// ============================================================================

/// 生成语音
#[command]
pub async fn bowong_generate_speech(
    request: SpeechGenerationRequest,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Speech generated successfully".to_string(),
        data: Some(serde_json::json!({
            "audio_url": "https://example.com/generated-speech.mp3"
        })),
    })
}

/// 获取声音列表
#[command]
pub async fn bowong_get_voices() -> Result<VoiceListResponse, String> {
    Ok(VoiceListResponse {
        voices: vec![
            VoiceInfo {
                id: "voice-1".to_string(),
                name: "Female Voice 1".to_string(),
                language: "zh-CN".to_string(),
                gender: "female".to_string(),
            },
            VoiceInfo {
                id: "voice-2".to_string(),
                name: "Male Voice 1".to_string(),
                language: "zh-CN".to_string(),
                gender: "male".to_string(),
            },
        ],
    })
}

/// 上传音频文件
#[command]
pub async fn bowong_upload_audio_file(
    request: AudioFileUploadRequest,
) -> Result<FileUploadResponse, String> {
    Ok(FileUploadResponse {
        status: true,
        msg: "Audio file uploaded successfully".to_string(),
        data: Some("https://example.com/uploaded-audio.mp3".to_string()),
    })
}

/// 克隆声音
#[command]
pub async fn bowong_clone_voice(
    request: VoiceCloneRequest,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Voice cloned successfully".to_string(),
        data: Some(serde_json::json!({
            "voice_id": "cloned-voice-456",
            "voice_name": request.voice_name
        })),
    })
}

// ============================================================================
// 聚合接口模块命令
// ============================================================================

/// 获取图片模型列表
#[command]
pub async fn bowong_get_image_model_list() -> Result<ModelListResponse, String> {
    Ok(ModelListResponse {
        models: vec![
            ModelInfo {
                id: "midjourney".to_string(),
                name: "Midjourney".to_string(),
                description: Some("AI image generation model".to_string()),
                capabilities: vec!["image_generation".to_string(), "image_variation".to_string()],
            },
            ModelInfo {
                id: "dalle3".to_string(),
                name: "DALL-E 3".to_string(),
                description: Some("OpenAI's image generation model".to_string()),
                capabilities: vec!["image_generation".to_string()],
            },
        ],
    })
}

/// 聚合同步生成图片
#[command]
pub async fn bowong_union_sync_generate_image(
    request: UnionImageGenerationRequest,
) -> Result<ImageGenerationResponse, String> {
    Ok(ImageGenerationResponse {
        task_id: "union-img-123".to_string(),
        status: "completed".to_string(),
        images: Some(vec!["https://example.com/union-image.jpg".to_string()]),
        error: None,
    })
}

/// 获取视频模型列表
#[command]
pub async fn bowong_get_video_model_list() -> Result<ModelListResponse, String> {
    Ok(ModelListResponse {
        models: vec![
            ModelInfo {
                id: "jimeng".to_string(),
                name: "极梦".to_string(),
                description: Some("AI video generation model".to_string()),
                capabilities: vec!["video_generation".to_string()],
            },
            ModelInfo {
                id: "veo".to_string(),
                name: "VEO".to_string(),
                description: Some("Advanced video generation model".to_string()),
                capabilities: vec!["video_generation".to_string(), "video_editing".to_string()],
            },
        ],
    })
}

/// 聚合异步生成视频
#[command]
pub async fn bowong_union_async_generate_video(
    request: UnionVideoGenerationRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "union-video-456".to_string(),
        status: "pending".to_string(),
        message: "Union video generation task submitted".to_string(),
    })
}

// ============================================================================
// ComfyUI 工作流模块命令
// ============================================================================

/// 获取运行节点
#[command]
pub async fn bowong_get_running_node(
    params: Option<GetRunningNodeParams>,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Running nodes retrieved".to_string(),
        data: Some(serde_json::json!({
            "nodes": [
                {"id": "node-1", "status": "running"},
                {"id": "node-2", "status": "idle"}
            ]
        })),
    })
}

/// 提交 ComfyUI 任务
#[command]
pub async fn bowong_submit_comfyui_task(
    request: ComfyUITaskRequest,
) -> Result<TaskResponse, String> {
    Ok(TaskResponse {
        task_id: "comfyui-task-789".to_string(),
        status: "pending".to_string(),
        message: "ComfyUI task submitted".to_string(),
    })
}

/// 查询 ComfyUI 任务状态
#[command]
pub async fn bowong_query_comfyui_task_status(
    params: ComfyUITaskStatusParams,
) -> Result<TaskStatusResponse, String> {
    Ok(TaskStatusResponse {
        task_id: params.task_id,
        status: "completed".to_string(),
        progress: Some(100.0),
        result: Some(serde_json::json!({
            "output_images": ["https://example.com/comfyui-output.jpg"]
        })),
        error: None,
        created_at: Some("2024-01-01T00:00:00Z".to_string()),
        updated_at: Some("2024-01-01T00:00:00Z".to_string()),
    })
}

/// 同步执行工作流
#[command]
pub async fn bowong_sync_execute_workflow(
    request: ComfyUISyncExecuteRequest,
) -> Result<ApiResponse, String> {
    Ok(ApiResponse {
        status: true,
        message: "Workflow executed successfully".to_string(),
        data: Some(serde_json::json!({
            "execution_id": "exec-123",
            "outputs": ["https://example.com/workflow-output.jpg"]
        })),
    })
}

// ============================================================================
// 批量操作命令
// ============================================================================

/// 批量取消任务
#[command]
pub async fn bowong_cancel_tasks(
    task_ids: Vec<String>,
) -> Result<Vec<ApiResponse>, String> {
    let results = task_ids.into_iter().map(|task_id| {
        ApiResponse {
            status: true,
            message: format!("Task {} cancelled", task_id),
            data: None,
        }
    }).collect();

    Ok(results)
}

/// 批量查询任务状态
#[command]
pub async fn bowong_batch_query_task_status(
    task_ids: Vec<String>,
) -> Result<Vec<TaskStatusResponse>, String> {
    let results = task_ids.into_iter().map(|task_id| {
        TaskStatusResponse {
            task_id,
            status: "completed".to_string(),
            progress: Some(100.0),
            result: Some(serde_json::json!({"output": "success"})),
            error: None,
            created_at: Some("2024-01-01T00:00:00Z".to_string()),
            updated_at: Some("2024-01-01T00:00:00Z".to_string()),
        }
    }).collect();

    Ok(results)
}

/// 等待任务完成
#[command]
pub async fn bowong_wait_for_task_completion(
    state: State<'_, AppState>,
    task_id: String,
    max_wait_time: u64,
    poll_interval: u64,
) -> Result<TaskStatusResponse, String> {
    // 克隆服务以避免跨 await 点持有 MutexGuard
    let service = {
        let service_guard = state.get_bowong_service()
            .map_err(|e| format!("Failed to get BowongTextVideoAgent service: {}", e))?;

        service_guard.as_ref()
            .ok_or_else(|| "BowongTextVideoAgent service not initialized".to_string())?
            .clone()
    };

    // 使用真实的轮询逻辑等待任务完成
    service.wait_for_task_completion(&task_id, max_wait_time, poll_interval)
        .await
        .map_err(|e| format!("Failed to wait for task completion: {}", e))
}

// ============================================================================
// Hedra 口型合成模块命令
// ============================================================================

/// Hedra 上传文件
#[command]
pub async fn hedra_upload_file(
    state: State<'_, AppState>,
    params: HedraFileUploadRequest,
) -> Result<FileUploadResponse, String> {
    // 克隆服务以避免跨 await 点持有 MutexGuard
    let service = {
        let service_guard = state.get_bowong_service()
            .map_err(|e| format!("Failed to get BowongTextVideoAgent service: {}", e))?;

        service_guard.as_ref()
            .ok_or_else(|| "BowongTextVideoAgent service not initialized".to_string())?
            .clone()
    };

    service.hedra_upload_file(&params)
        .await
        .map_err(|e| format!("Failed to upload file to Hedra: {}", e))
}

/// Hedra 提交任务
#[command]
pub async fn hedra_submit_task(
    state: State<'_, AppState>,
    params: HedraTaskSubmitRequest,
) -> Result<TaskResponse, String> {
    println!("=== Hedra Submit Task Command ===");
    println!("接收到的参数: {:?}", params);
    println!("img_file: {:?}", params.img_file);
    println!("audio_file: {:?}", params.audio_file);
    println!("params 字段: {:?}", params.params);

    // 克隆服务以避免跨 await 点持有 MutexGuard
    let service = {
        let service_guard = state.get_bowong_service()
            .map_err(|e| format!("Failed to get BowongTextVideoAgent service: {}", e))?;

        service_guard.as_ref()
            .ok_or_else(|| "BowongTextVideoAgent service not initialized".to_string())?
            .clone()
    };

    println!("=== 调用服务方法 ===");
    let result = service.hedra_submit_task(&params)
        .await
        .map_err(|e| {
            println!("=== 服务调用失败 ===");
            println!("错误信息: {}", e);
            format!("Failed to submit Hedra task: {}", e)
        });

    if let Ok(ref response) = result {
        println!("=== 服务调用成功 ===");
        println!("响应: {:?}", response);
    }

    result
}

/// Hedra 查询任务状态
#[command]
pub async fn hedra_query_task_status(
    state: State<'_, AppState>,
    params: HedraTaskStatusParams,
) -> Result<TaskStatusResponse, String> {
    // 克隆服务以避免跨 await 点持有 MutexGuard
    let service = {
        let service_guard = state.get_bowong_service()
            .map_err(|e| format!("Failed to get BowongTextVideoAgent service: {}", e))?;

        service_guard.as_ref()
            .ok_or_else(|| "BowongTextVideoAgent service not initialized".to_string())?
            .clone()
    };

    service.hedra_query_task_status(&params)
        .await
        .map_err(|e| format!("Failed to query Hedra task status: {}", e))
}
